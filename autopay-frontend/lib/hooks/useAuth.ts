'use client'

import type { Permission } from '@/lib/types/permissions'
import { apiQueryFetch } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import Cryptojs from 'crypto-js'
import Cookies from 'js-cookie'
import { useEffect, useState } from 'react'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [needFetch, setNeedFetch] = useState(false)

  // Use "react-query" to fetch user data from the API with permissions
  const { data: userDataFromServer } = useQuery<ApiResponse<User>>({
    queryKey: ['getUserProfile'],
    queryFn: () => apiQueryFetch('/profile'),
    enabled: needFetch,
  })

  useEffect(() => {
    // Check if authorization token exists first
    const authToken = Cookies.get(process.env.NEXT_PUBLIC_APP_NAME + '.authorization')

    if (!authToken) {
      // No authorization token, user is not authenticated
      setUser(null)
      setLoading(false)
      setNeedFetch(false)
      return
    }

    // Get user data from cookies
    const userCookie = Cookies.get(process.env.NEXT_PUBLIC_APP_NAME + '.user')

    if (userCookie) {
      try {
        const userData = JSON.parse(userCookie)
        const emailMd5 = Cryptojs.MD5(userData.email).toString()

        const userObject = {
          id: userData.id,
          name: userData.name || '',
          email: userData.email || '',
          email_md5: userData.email_md5 || emailMd5,
          gravatar: 'https://gravatar.com/avatar/' + emailMd5,
          timezone: userData.timezone || 'UTC',
          ...userData, // Include any additional user properties
        }

        setUser(userObject)

        // If user object is missing critical organization info, fetch from server
        if (!userData.current_organization.alias) {
          setNeedFetch(true)
        }
      } catch (error) {
        // If user cookie is corrupted but auth token exists, fetch from server
        setNeedFetch(true)
      }
    } else {
      // No user cookie but auth token exists, fetch from server
      setNeedFetch(true)
    }
  }, [])

  useEffect(() => {
    if (userDataFromServer?.data) {
      setUser(userDataFromServer.data)
      Cookies.set(process.env.NEXT_PUBLIC_APP_NAME + '.user', JSON.stringify(userDataFromServer.data))
      setNeedFetch(false) // Stop fetching once we have data
    }
  }, [userDataFromServer?.data])

  // This effect will run whenever 'user' changes
  useEffect(() => {
    // Check if we have auth token
    const authToken = Cookies.get(process.env.NEXT_PUBLIC_APP_NAME + '.authorization')

    // Set loading to false when:
    // 1. User is set (authenticated)
    // 2. No auth token exists (not authenticated)
    // 3. needFetch is false (no pending fetch operation)
    if (user !== null || !authToken || !needFetch) {
      setLoading(false)
    }
  }, [user, needFetch])

  // Function to update user data in cookie and state
  const updateUser = (userData: Partial<User>) => {
    try {
      // Get current cookie
      const userCookie = Cookies.get(process.env.NEXT_PUBLIC_APP_NAME + '.user')

      // Case 1: User exists in state but no cookie - create new cookie with current user and updates
      // Case 2: Cookie exists - update existing cookie
      // Case 3: No cookie and no user in state, but have required fields - create new user cookie

      let updatedUserData: User | Record<string, any>

      if (userCookie) {
        // Parse current cookie data
        const currentUserData = JSON.parse(userCookie)

        // Update with new data
        updatedUserData = {
          ...currentUserData,
          ...userData,
        }
      } else if (user) {
        // User state exists, but the cookie doesn't
        // Create a new cookie with current user data + updates
        updatedUserData = {
          ...user,
          ...userData,
        }
      } else {
        // No user in state and no cookie, but have minimum required fields
        // Create new user cookie with provided data
        updatedUserData = {
          ...userData,
        } as User
      }

      // Recalculate MD5 if an email exists or was updated
      if (updatedUserData.email) {
        const emailMd5 = Cryptojs.MD5(updatedUserData.email).toString()
        updatedUserData.email_md5 = emailMd5
        updatedUserData.gravatar = 'https://gravatar.com/avatar/' + emailMd5
      }

      // Update cookie
      Cookies.set(process.env.NEXT_PUBLIC_APP_NAME + '.user', JSON.stringify(updatedUserData))

      // Update the state if it exists, else create a new state
      if (user) {
        setUser({
          ...user,
          ...updatedUserData,
        })
      } else {
        setUser(updatedUserData as User)
      }

      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Get user permissions for current context
   */
  const getUserPermissions = (): Permission[] => {
    if (!user) return []

    return [...(user.permissions || []), ...(user.organization_permissions || []), ...(user.team_permissions || [])]
  }

  /**
   * Check if user has specific permission
   */
  const hasPermission = (permission: Permission): boolean => {
    const permissions = getUserPermissions()
    return permissions.includes(permission)
  }

  /**
   * Check if user is owner in current context
   */
  const isOwner = (): boolean => {
    if (!user) return false
    return user.is_owner || false
  }

  return {
    user,
    loading,
    isAuthenticated: !!user,
    updateUser,
    getUserPermissions,
    hasPermission,
    isOwner,
  }
}

export default useAuth
