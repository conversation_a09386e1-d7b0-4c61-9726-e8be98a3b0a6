import { apiQueryFetch } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import Cookies from 'js-cookie'

export const useUser = () => {
  // Check if authorization token exists before making request
  const authToken = Cookies.get(process.env.NEXT_PUBLIC_APP_NAME + '.authorization')

  const { data, isLoading, error } = useQuery<ApiResponse<User>>({
    queryKey: ['getUserProfile'],
    queryFn: () => apiQueryFetch('/profile'),
    enabled: !!authToken, // Only fetch if auth token exists
  })

  if (isLoading || error) {
    return { user: null, isLoading, error }
  }

  return {
    user: data?.data ?? null,
    isLoading,
    error,
  }
}
