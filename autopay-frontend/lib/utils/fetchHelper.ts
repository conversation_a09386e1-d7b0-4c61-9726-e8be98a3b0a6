'use client'

import { getCookie } from '@/lib/utils/cookieServerHelper'
import jsCookie from 'js-cookie'
import { get, has } from 'lodash'

type RequestOptions = Omit<RequestInit, 'headers'> & {
  headers?: Record<string, string>
  timeout?: number // Timeout in milliseconds
  customApiUrl?: string // Allow custom API URL override
}

const isClientSide = typeof window !== 'undefined'

// Global API URL state for client-side usage
let globalApiUrl: string | null = null

/**
 * Set the global API URL for client-side requests
 */
export function setGlobalApiUrl(apiUrl: string): void {
  globalApiUrl = apiUrl
  console.log('🔄 API URL updated to:', apiUrl)
}

/**
 * Get the current API URL
 */
export function getCurrentApiUrl(): string {
  return globalApiUrl || process.env.NEXT_PUBLIC_API_URL || ''
}

/**
 * Reset API URL to default
 */
export function resetApiUrl(): void {
  globalApiUrl = null
  console.log('🔄 API URL reset to default:', process.env.NEXT_PUBLIC_API_URL)
}

// ! Do not throw any errors in this function, or will be server-side runtime error 500
export const fetchHelper = async (url: string, options: RequestOptions = {}): Promise<ApiResponse> => {
  // Set the authorization bearer token if the request is made from the client side to the API server
  const { customApiUrl, ...fetchOptions } = options
  const baseApiUrl = customApiUrl || globalApiUrl || process.env.NEXT_PUBLIC_API_URL || ''
  const requestUrl = url.startsWith('http') ? url : baseApiUrl + url
  const authorizationTokenFromCookieServer = await getCookie('.authorization')

  const authorizationToken = isClientSide
    ? jsCookie.get(process.env.NEXT_PUBLIC_APP_NAME + '.authorization')
    : authorizationTokenFromCookieServer

  const defaultHeaders = {
    'Content-Type': 'application/json',
    Authorization: 'Bearer ' + authorizationToken,
  }

  const headers = {
    ...defaultHeaders,
    ...options.headers,
  }

  const { timeout, ...restOptions } = fetchOptions
  const config: RequestInit = {
    ...restOptions,
    headers,
  }

  try {
    // Add timeout support
    const controller = new AbortController()
    const timeoutId = timeout ? setTimeout(() => controller.abort(), timeout) : null

    const response = await fetch(requestUrl, {
      ...config,
      signal: controller.signal,
    })

    // Clear timeout if request completes
    if (timeoutId) clearTimeout(timeoutId)
    let data = (await response.json()) as ApiResponse

    if (!response.ok) {
      // Check if the data has "data.code === '2FA_REQUIRED" then redirect to the 2FA page
      if (has(data, 'data.code') && get(data, 'data.code') === '2FA_REQUIRED' && isClientSide) {
        // Save the current url to the local storage
        localStorage.setItem('redirectUrl', window.location.href)
        window.location.href = '/auth/2fa'

        return data
      }

      if (
        response.status === 401 &&
        isClientSide &&
        process.env.NEXT_PUBLIC_SHOULD_LOG_OUT_WHEN_RECEIVE_UNAUTHORIZED_STATUS === 'true'
      ) {
        jsCookie.remove(process.env.NEXT_PUBLIC_APP_NAME + '.authorization')
        location.reload()
      }

      data = {
        ...data,
        ...{
          success: false,
          code: response.status,
        },
      }
    }

    return data
  } catch (error) {
    // Handle timeout and other errors
    let message = 'An unexpected error occurred'
    let errorCode = 'unexpected_error'

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        message = 'Request timeout'
        errorCode = 'timeout'
      } else {
        message = error.message
      }
    } else if (has(error, 'message') && typeof get(error, 'message') === 'string') {
      message = get(error, 'message')
    }

    if (has(error, 'statusText') && typeof get(error, 'statusText') === 'string') {
      errorCode = get(error, 'statusText')
    }

    return {
      success: false,
      message,
      data: {
        errorCode: errorCode,
      },
    }
  }
}

// Fetch data from the API server with "error handling" from useQuery
export const queryFetchHelper = async (url: string, options: RequestOptions = {}): Promise<any> => {
  const response = await fetchHelper(url, options)

  if (!response.success) {
    throw response
  }
  return response
}

/**
 * Enhanced fetch helper that uses the global API URL (alias for fetchHelper)
 */
export const apiFetch = fetchHelper

/**
 * Enhanced query fetch helper that uses the global API URL (alias for queryFetchHelper)
 */
export const apiQueryFetch = queryFetchHelper
