'use client'

import { apiFetch } from '@/lib/utils/fetchHelper'
import { useState } from 'react'

export function ApiTest(): React.JSX.Element {
  const [testResult, setTestResult] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  const testApiCall = async (): Promise<void> => {
    setIsLoading(true)
    setTestResult('Testing...')

    try {
      const response = await apiFetch('/profile')
      setTestResult(`✅ Success: ${JSON.stringify(response, null, 2)}`)
    } catch (error: any) {
      setTestResult(`❌ Error: ${error.message || JSON.stringify(error, null, 2)}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="rounded-lg border bg-gray-50 p-4">
      <h3 className="mb-2 font-bold">🧪 API Test</h3>

      <button
        onClick={testApiCall}
        disabled={isLoading}
        className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:opacity-50">
        {isLoading ? 'Testing...' : 'Test API Call'}
      </button>

      {testResult && (
        <div className="mt-4 rounded border bg-white p-3 text-xs">
          <pre className="whitespace-pre-wrap">{testResult}</pre>
        </div>
      )}
    </div>
  )
}
