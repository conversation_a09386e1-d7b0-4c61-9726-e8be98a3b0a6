'use client'

import { useDomain } from '@/lib/hooks/use-domain'
import { getCurrentApiUrl } from '@/lib/utils/apiHelper'
import { useEffect, useState } from 'react'

export function ApiDebug(): React.JSX.Element {
  const { config } = useDomain()
  const [currentApiUrl, setCurrentApiUrl] = useState<string>('')

  useEffect(() => {
    const updateApiUrl = () => {
      setCurrentApiUrl(getCurrentApiUrl())
    }

    updateApiUrl()

    // Update every second to show real-time changes
    const interval = setInterval(updateApiUrl, 1000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="fixed right-4 bottom-4 max-w-sm rounded-lg bg-black/80 p-4 text-xs text-white">
      <div className="mb-2 font-bold">🔧 API Debug Info</div>

      <div className="space-y-1">
        <div>
          <span className="text-gray-300">Default API:</span>
          <br />
          <span className="text-green-400">{process.env.NEXT_PUBLIC_API_URL}</span>
        </div>

        <div>
          <span className="text-gray-300">Current API:</span>
          <br />
          <span className="text-blue-400">{currentApiUrl}</span>
        </div>

        {config?.backend_hostname && (
          <div>
            <span className="text-gray-300">Backend Domain:</span>
            <br />
            <span className="text-yellow-400">{config.backend_hostname}</span>
          </div>
        )}

        {config?.frontend_hostname && (
          <div>
            <span className="text-gray-300">Frontend Domain:</span>
            <br />
            <span className="text-purple-400">{config.frontend_hostname}</span>
          </div>
        )}

        <div>
          <span className="text-gray-300">Status:</span>
          <br />
          <span className={config?.backend_hostname ? 'text-green-400' : 'text-gray-400'}>
            {config?.backend_hostname ? '🟢 Using Custom Domain' : '🔵 Using Default API'}
          </span>
        </div>
      </div>
    </div>
  )
}
